//
//  NSObject+FMFoundation.h
//  FMFoundation
//
//  Created by Subo on 2018/12/18.
//  Copyright © 2018年 Subo. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSObject (FMFoundation)

/**
 交换实例方法

 @param origSel 原来的方法
 @param altSel 替换的方法
 @return 方法交换成功，返回YES；否则，返回NO
 */
+ (BOOL)fm_swizzleMethod:(SEL)origSel withMethod:(SEL)altSel;


/**
 交换类方法

 @param origSel 原来的方法
 @param altSel 替换的方法
 @return 方法交换成功，返回YES；否则，返回NO
 */
+ (BOOL)fm_swizzleClassMethod:(SEL)origSel withMethod:(SEL)altSel;

@end

NS_ASSUME_NONNULL_END
