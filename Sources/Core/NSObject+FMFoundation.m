//
//  NSObject+FMFoundation.m
//  FMFoundation
//
//  Created by Subo on 2018/12/18.
//  Copyright © 2018年 Subo. All rights reserved.
//

#import "NSObject+FMFoundation.h"
#import <objc/runtime.h>

@implementation NSObject (FMFoundation)

+ (BOOL)fm_swizzleMethod:(SEL)originalSelector withMethod:(SEL)swizzledSelector {
    Class class = [self class];
    
    Method originalMethod = class_getInstanceMethod(class, originalSelector);
    Method swizzledMethod = class_getInstanceMethod(class, swizzledSelector);
    
    BOOL didAddMethod =
    class_addMethod(class,
                    originalSelector,
                    method_getImplementation(swizzledMethod),
                    method_getTypeEncoding(swizzledMethod));
    if (didAddMethod) {
        class_replaceMethod(class,
                            swizzledSelector,
                            method_getImplementation(originalMethod),
                            method_getTypeEncoding(originalMethod));
    }else {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
    return YES;
}

+ (BOOL)fm_swizzleClassMethod:(SEL)origSel withMethod:(SEL)altSel {
    return [object_getClass((id)self) fm_swizzleMethod:origSel withMethod:altSel];
}

@end
