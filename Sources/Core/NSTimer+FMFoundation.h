//
//  NSTimer+FMFoundation.h
//  FMFoundation
//
//  Created by Subo on 2018/12/18.
//  Copyright © 2018年 Subo. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSTimer (FMFoundation)

+ (NSTimer *)fm_scheduledTimerWithTimeInterval:(NSTimeInterval)interval block:(void (^)(NSTimer *timer))block repeats:(BOOL)repeats;
+ (NSTimer *)fm_timerWithTimeInterval:(NSTimeInterval)interval block:(void (^)(NSTimer *timer))block repeats:(BOOL)repeats;

@end

NS_ASSUME_NONNULL_END
