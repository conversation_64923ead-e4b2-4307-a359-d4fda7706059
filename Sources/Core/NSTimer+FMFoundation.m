//
//  NSTimer+FMFoundation.m
//  FMFoundation
//
//  Created by Subo on 2018/12/18.
//  Copyright © 2018年 Subo. All rights reserved.
//

#import "NSTimer+FMFoundation.h"

@implementation NSTimer (FMFoundation)

+ (void)_fm_ExecBlock:(NSTimer *)timer {
    if ([timer userInfo]) {
        void (^block)(NSTimer *timer) = (void (^)(NSTimer *timer))[timer userInfo];
        block(timer);
    }
}

+ (NSTimer *)fm_scheduledTimerWithTimeInterval:(NSTimeInterval)interval block:(void (^)(NSTimer *timer))block repeats:(BOOL)repeats {
    return [NSTimer scheduledTimerWithTimeInterval:interval target:self selector:@selector(_fm_ExecBlock:) userInfo:[block copy] repeats:repeats];
}

+ (NSTimer *)fm_timerWithTimeInterval:(NSTimeInterval)interval block:(void (^)(NSTimer *timer))block repeats:(BOOL)repeats {
    return [NSTimer timerWithTimeInterval:interval target:self selector:@selector(_fm_ExecBlock:) userInfo:[block copy] repeats:repeats];
}

@end
