//
//  ToolInject.h
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>


void HookMethod(Class originalCls,Class swizzledCls,SEL originalSelector, SEL swizzledSelector);


@interface ToolInject : NSObject

NSString *createData(NSString *email, NSString *licenceKey, NSTimeInterval validFrom, NSTimeInterval validTill);

+ (id)callClassMethod:(SEL)selector onClass:(Class)cls;
+ (id)callInstanceMethod:(SEL)selector onObject:(id)object;
+ (id)callInstanceMethod:(SEL)selector onObject:(id)object withArguments:(NSArray *)arguments;
+ (id)createInstanceWithClass:(Class)cls selector:(SEL)selector withArguments:(NSArray *)arguments;
+ (NSString *) boolToString:(BOOL)value;


@end
