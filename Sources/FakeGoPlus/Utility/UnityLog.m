//
//  UnityLog.m
//  UnityPlugin
//
//  Created by lsc on 2025/5/12.
//

#import <Foundation/Foundation.h>
#import "UnityLog.h"



@implementation UnityLog

+ (instancetype)sharedInstance {
    static UnityLog *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[UnityLog alloc] init];
        instance.logFilePath = [NSHomeDirectory() stringByAppendingPathComponent:@"Downloads/log_unity.txt"];
    });
    return instance;
}

- (void)logMessage:(NSString *)message level:(LogLevel)level {
    NSString *levelString;
    switch (level) {
        case LogLevelInfo:
            levelString = @"INFO";
            break;
        case LogLevelWarning:
            levelString = @"WARNING";
            break;
        case LogLevelError:
            levelString = @"ERROR";
            break;
    }

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSString *dateString = [formatter stringFromDate:[NSDate date]];
    NSString *logMessage = [NSString stringWithFormat:@"%@ [%@] %@\n", dateString, levelString, message];
    [self writeLogToFile:logMessage];
}

- (void)writeLogToFile:(NSString *)logMessage {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:self.logFilePath]) {
        [fileManager createFileAtPath:self.logFilePath contents:nil attributes:nil];
    }

    NSFileHandle *fileHandle = [NSFileHandle fileHandleForWritingAtPath:self.logFilePath];
    if (fileHandle) {
        [fileHandle seekToEndOfFile];
        [fileHandle writeData:[logMessage dataUsingEncoding:NSUTF8StringEncoding]];
        [fileHandle closeFile];
    }
}

- (void)clearLogFile {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:self.logFilePath]) {
        [fileManager removeItemAtPath:self.logFilePath error:nil];
        [fileManager createFileAtPath:self.logFilePath contents:nil attributes:nil];
    }
}

@end


