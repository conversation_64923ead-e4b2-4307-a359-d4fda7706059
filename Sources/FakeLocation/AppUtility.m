//
//  AppUtility.m
//  data
//
//  Created by Subo on 2024/6/28.
//

#import "AppUtility.h"
#import <UIKit/UIKit.h>
#import <mach-o/dyld.h>
#import <stdio.h>
#import <dlfcn.h>
#import "JCfdal886fdaallmd.h"
#import <objc/runtime.h>
#import <CommonCrypto/CommonDigest.h>
#import "JCfdal886fdaallmd.h"
#import "NSString+AGExtensions.h"
#import "AGConst.h"

@implementation AppUtility

static int kCurrentCount = 0;
static BOOL kMenuButtonIsHidden = NO;

+ (void)load {
//    [self mid_sub_45513];

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(40 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [JCfdal886fdaallmd.mid_sub_45522 mid_sub_45523];

        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(30 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [JCfdal886fdaallmd.mid_sub_45522 mid_sub_45523];
        });
    });

    // checkValid
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//            [self mid_sub_45512];
//        });
//    });
}

// checkValid
+ (void)mid_sub_45512 {
    DebugLog(@"Start checking...");
    NSString *libPath = [[NSBundle mainBundle] pathForResource:@"libdata"
                                                        ofType:@"dylib"
                                                   inDirectory:@"Frameworks"];
    if (![NSFileManager.defaultManager fileExistsAtPath:libPath]) {
        DebugLog(@"libdata file not exist.");
        exit(0);
        return;
    }

    NSString *dataPath = [[NSBundle mainBundle] pathForResource:@"data" ofType:nil inDirectory:@"Frameworks"];
    if (![NSFileManager.defaultManager fileExistsAtPath:dataPath]) {
        DebugLog(@"data file not exist.");
        exit(0);
        return;
    }

    NSError *error = nil;
    NSString *data = [NSString stringWithContentsOfFile:dataPath encoding:NSUTF8StringEncoding error:&error];
    if (!data || data.length == 0) {
        DebugLog(@"解析data失败： %@.", error);
        exit(0);
        return;
    }
    DebugLog(@"Read data file: %@.", data);

    NSString *fileMD5 = [NSString mid_sub_45511:libPath];
    DebugLog(@"fileMD5: %@.", fileMD5);
    // fileMD5 + iWherego 求值 md5
    NSString *salt = @"iWherego";
    NSString *truthMD5 = [NSString stringWithFormat:@"%@%@", fileMD5, salt];
    truthMD5 = truthMD5.mid_sub_45510;
    DebugLog(@"Value MD5: %@.", truthMD5);
    if (![data isEqualToString:truthMD5]) {
        DebugLog(@"Value MD5 is not equal.");
        exit(0);
        return;
    }
    DebugLog(@"Check success.");
}

+ (void)printMethodsIfNeeded {
    Class cls = NSClassFromString(@"IGP_LocationSimulator");
    if (!cls) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self printMethodsIfNeeded];
        });
        return;
    }

    [self printMethodsAndPropertiesOfClass:cls];
}

+ (void)printMethodsAndPropertiesOfClass:(Class)cls {
    unsigned int methodCount = 0;
    Method *methods = class_copyMethodList(cls, &methodCount);

    DebugLog(@"Methods for class %s:", class_getName(cls));
    for (unsigned int i = 0; i < methodCount; i++) {
        Method method = methods[i];
        SEL selector = method_getName(method);
        const char *methodName = sel_getName(selector);
        DebugLog(@"  - %s", methodName);

        unsigned int argumentCount = method_getNumberOfArguments(method);
        for (unsigned int j = 0; j < argumentCount; j++) {
            char argumentType[256];
            method_getArgumentType(method, j, argumentType, sizeof(argumentType));
            DebugLog(@"    Argument %d type: %s", j, argumentType);
        }
    }
    free(methods);

    unsigned int propertyCount = 0;
    objc_property_t *properties = class_copyPropertyList(cls, &propertyCount);

    DebugLog(@" Properties for class %s:", class_getName(cls));
    for (unsigned int i = 0; i < propertyCount; i++) {
        objc_property_t property = properties[i];
        const char *propertyName = property_getName(property);
        DebugLog(@"  - %s", propertyName);
    }
    free(properties);
}

// 每隔1s检测一次
// trackHiddenButton
+ (void)mid_sub_45513 {
    DebugLog(@"Start trackHiddenButton");
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        kCurrentCount += 1;
        [self mid_sub_45514];
    });
}

// startHiddenButton
+ (void)mid_sub_45514 {
    UIWindow *keyWindow = [self findKeyWindow];
    [self mid_sub_45515:keyWindow];
    if (!kMenuButtonIsHidden) {
        [self mid_sub_45515:keyWindow.rootViewController.view];
    }

    [self mid_sub_45513];
}

// findMenuButtonInView
+ (void)mid_sub_45515:(UIView *)view {
    if ([view isKindOfClass:NSClassFromString(@"DWBubbleMenuButton")] ||
        [view isKindOfClass:NSClassFromString(@"JoystickController")]) {
        view.hidden = YES;
        DebugLog(@"Did find DWBubbleMenuButton in view: %@", view);
        kMenuButtonIsHidden = YES;
    }

    for (UIView *subview in view.subviews) {
        [self mid_sub_45515:subview];
    }
}

+ (UIWindow *)findKeyWindow {
    UIWindow *keyWindow = nil;
    for (UIWindowScene *windowScene in [UIApplication sharedApplication].connectedScenes) {
        if (windowScene.activationState == UISceneActivationStateForegroundActive) {
            keyWindow = windowScene.windows.firstObject;
            break;
        }
    }

    if (keyWindow) {
        return keyWindow;
    }

    for(UIWindow *window in UIApplication.sharedApplication.windows) {
        if (window.isKeyWindow) {
            return window;
        }
    }

    return nil;
}

@end
