//
//  AGLocationManager.m
//  data
//
//  Created by Sub<PERSON> on 2024/7/2.
//

#import "JCfdal886fdaallmd.h"
#import <objc/runtime.h>
#import <CoreLocation/CoreLocation.h>
#import "AppUtility.h"
#import "AGConst.h"

@class IGP_LocationSimulator;

static char kLocationObjectKey;

@interface JCfdal886fdaallmd ()<CLLocationManagerDelegate>

// locationManager
@property(nonatomic, strong) CLLocationManager *cjjmddxaax;
// isInited
@property(nonatomic, assign) BOOL aisxxmifx;

@end

@implementation JCfdal886fdaallmd

+ (instancetype)mid_sub_45522 {
    static JCfdal886fdaallmd *_sharedIntance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedIntance = [[JCfdal886fdaallmd alloc] init];
    });
    return _sharedIntance;
}

- (void)mid_sub_45523 {
    if (self.aisxxmifx) {
        return;
    }
    DebugLog(@"startChangeLC");
    [self mid_sub_45524];
}

// createLocationManager
- (void)mid_sub_45524 {
    DebugLog(@"createLocationManager");
    self.cjjmddxaax = [[CLLocationManager alloc] init];
    self.cjjmddxaax.delegate = self;
    [self.cjjmddxaax requestWhenInUseAuthorization];
    [self.cjjmddxaax startUpdatingLocation];
}

// setCurrentLocation
- (void)mid_sub_45525:(CLLocationCoordinate2D)location {
    // 动态获取 IGP_LocationSimulator 类并调用方法
    Class locationSimulatorClass = NSClassFromString(@"IGP_LocationSimulator");
    if (!locationSimulatorClass) {
        DebugLog(@"Get IGP_LocationSimulator class failed");
        return;
    }
    // sharedManager
    SEL sharedSelector = NSSelectorFromString(@"sharedManager");
    id locationSimulator = [locationSimulatorClass performSelector:sharedSelector];
    if (locationSimulator) {
        DebugLog(@"Get IGP_LocationSimulator sharedManager success");
        SEL selector = NSSelectorFromString(@"TeleportToLocationNew:");
        if ([locationSimulator respondsToSelector:selector]) {
            DebugLog(@"Start invoke IGP_LocationSimulator TeleportToLocationNew: method");
            // 获取方法签名
            NSMethodSignature *signature = [locationSimulator methodSignatureForSelector:selector];
            // 创建NSInvocation
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
            // 设置接收者
            [invocation setTarget:locationSimulator];
            // 设置选择器
            [invocation setSelector:selector];
            // 设置参数
            [invocation setArgument:&location atIndex:2]; // 注意：参数索引从2开始，因为0和1被self和_cmd占用

            // 调用方法
            [invocation invoke];
        } else {
            DebugLog(@"Get IGP_LocationSimulator TeleportToLocationNew: failed");
        }
        SEL updateSelector = NSSelectorFromString(@"UpdateLocation");
        if ([locationSimulator respondsToSelector:updateSelector]) {
            [locationSimulator performSelector:updateSelector];
            DebugLog(@"Start invoke IGP_LocationSimulator UpdateLocation method");
        } else {
            DebugLog(@"Get IGP_LocationSimulator UpdateLocation failed");
        }
    } else {
        DebugLog(@"Get IGP_LocationSimulator sharedManager failed");
    }
}

// MARK: - CLLocationManagerDelegate

- (void)locationManagerDidChangeAuthorization:(CLLocationManager *)manager {
    switch (manager.authorizationStatus) {
        case kCLAuthorizationStatusNotDetermined: {
            DebugLog(@"didChangeAuthorizationStatus NotDetermined");
            // 主动获得授权
            [self.cjjmddxaax requestWhenInUseAuthorization];
            break;
        }
        case kCLAuthorizationStatusRestricted: {
            DebugLog(@"didChangeAuthorizationStatus Restricted");
            // 主动获得授权
            [self.cjjmddxaax requestWhenInUseAuthorization];
            break;
        }
        case kCLAuthorizationStatusDenied: {
            DebugLog(@"didChangeAuthorizationStatus Denied");
            break;
        }
        case kCLAuthorizationStatusAuthorizedAlways: {
            DebugLog(@"didChangeAuthorizationStatus Always");
            break;
        }
        case kCLAuthorizationStatusAuthorizedWhenInUse: {
            DebugLog(@"didChangeAuthorizationStatus WhenInUse");
            break;
        }
        default:
            break;
    }
}

- (void)locationManager:(CLLocationManager *)manager 
     didUpdateLocations:(NSArray<CLLocation *> *)locations {
    self.aisxxmifx = YES;
    CLLocation *location = locations.lastObject;
    CLLocationCoordinate2D coordinate = location.coordinate;
    DebugLog(@"Track update location: latitude: %f, %f", coordinate.latitude, coordinate.longitude);
    [self mid_sub_45525:coordinate];
}

// MARK: - Getter & Setter

@dynamic cjjmddxaax;
- (CLLocationManager *)cjjmddxaax {
    return objc_getAssociatedObject(self, &kLocationObjectKey);
}

- (void)setCjjmddxaax:(CLLocationManager *)locationManager {
    objc_setAssociatedObject(self, &kLocationObjectKey, locationManager, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

}

@end
