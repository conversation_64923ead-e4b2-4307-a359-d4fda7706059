//
//  NSObject+FCAlertViewHook.m
//  data
//
//  Created by Subo on 2024/6/29.
//

#import "NSObject+FCAlertViewHook.h"
#import <objc/runtime.h>
#import <CoreLocation/CoreLocation.h>

@implementation NSObject (FCAlertViewHook)

+ (void)ag_hookLocationCoordinat {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class targetClass = NSClassFromString(@"HLocationCoordinate");
        if (!targetClass) {
            NSLog(@"[=====] HLocationCoordinate not exists.");
            return;
        }

        NSLog(@"[=====] Did find HLocationCoordinate, start hooks");
        SEL originSelector1 = NSSelectorFromString(@"startChangeLC");
        SEL swizzledSelector1 = NSSelectorFromString(@"ag_startChangeLC");
        Method originMethod1 = class_getInstanceMethod(targetClass, originSelector1);
        Method swizzledMethod1 = class_getInstanceMethod(self, swizzledSelector1);
        if (originMethod1 && swizzledMethod1) {
            method_exchangeImplementations(originMethod1, swizzledMethod1);
        }

        SEL originSelector2 = @selector(locationManager:didUpdateLocations:);
        SEL swizzledSelector2 = @selector(ag_locationManager:didUpdateLocations:);
        Method originMethod2 = class_getInstanceMethod(targetClass, originSelector2);
        Method swizzledMethod2 = class_getInstanceMethod(self, swizzledSelector2);
        if (originMethod2 && swizzledMethod2) {
            method_exchangeImplementations(originMethod2, swizzledMethod2);
        }

        SEL originSelector3 = NSSelectorFromString(@"intLabel:");
        SEL swizzledSelector3 = @selector(ag_intLabel:);
        Method originMethod3 = class_getInstanceMethod(targetClass, originSelector3);
        Method swizzledMethod3 = class_getInstanceMethod(self, swizzledSelector3);
        if (originMethod3 && swizzledMethod3) {
            method_exchangeImplementations(originMethod3, swizzledMethod3);
        }
    });
}

- (void)ag_startChangeLC {
    NSLog(@"[=====] Track startChangeLC");
    [self printCallStack];
    [self ag_startChangeLC];
}

-  (void)ag_locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
//    NSLog(@"[=====] Track locationManager: didUpdateLocations");
//    CLLocation *location = locations.firstObject;
//    NSLog(@"[=====] location.latitude= %f, location.longitude = %f", location.coordinate.latitude, location.coordinate.longitude);
    [self ag_locationManager:manager didUpdateLocations:locations];
}

- (int)ag_intLabel:(id)tag {
    NSLog(@"[=====] Track intLabel: %@", tag);
    [self printCallStack];
    return [self ag_intLabel:tag];
}

- (void)printCallStack {
    NSLog(@"[=====] 调用堆栈信息");
    NSArray *callStack = [NSThread callStackSymbols];
    for (NSString *symbol in callStack) {
        NSLog(@"[=====] %@", symbol);
    }
}

//+ (void)ag_hookMethods {
//    static dispatch_once_t onceToken;
//    dispatch_once(&onceToken, ^{
//        Class targetClass = NSClassFromString(@"FCAlertView");
//        if (!targetClass) {
//            NSLog(@"[=====] FCAlertView not exists.");
//            return;
//        }
//
//        NSLog(@"[=====] Did find FCAlertView, start hooks");
//        SEL originSelector1 = NSSelectorFromString(@"showAlertInView:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector1 = @selector(hook_showAlertInView:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod1 = class_getInstanceMethod(targetClass, originSelector1);
//        Method swizzledMethod1 = class_getInstanceMethod(self, swizzledSelector1);
//        if (originMethod1 && swizzledMethod1) {
//            NSLog(@"[=====] Hook showAlertInView:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod1, swizzledMethod1);
//        }
//
//        SEL originSelector2 = NSSelectorFromString(@"showAlertInWindow:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector2 = @selector(hook_showAlertInWindow:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod2 = class_getInstanceMethod(targetClass, originSelector2);
//        Method swizzledMethod2 = class_getInstanceMethod(self, swizzledSelector2);
//        if (originMethod2 && swizzledMethod2) {
//            NSLog(@"[=====] Hook showAlertInWindow:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod2, swizzledMethod2);
//        }
//
//        SEL originSelector3 = NSSelectorFromString(@"showAlertWithTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector3 = @selector(hook_showAlertWithTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod3 = class_getInstanceMethod(targetClass, originSelector3);
//        Method swizzledMethod3 = class_getInstanceMethod(self, swizzledSelector3);
//        if (originMethod3 && swizzledMethod3) {
//            NSLog(@"[=====] Hook showAlertWithTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod3, swizzledMethod3);
//        }
//
//        SEL originSelector4 = NSSelectorFromString(@"showAlertWithAttributedTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector4 = @selector(hook_showAlertWithAttributedTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod4 = class_getInstanceMethod(targetClass, originSelector4);
//        Method swizzledMethod4 = class_getInstanceMethod(self, swizzledSelector4);
//        if (originMethod4 && swizzledMethod4) {
//            NSLog(@"[=====] Hook showAlertWithAttributedTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod4, swizzledMethod4);
//        }
//
//        SEL originSelector5 = NSSelectorFromString(@"showAlertWithTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector5 = @selector(hook_showAlertWithTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod5 = class_getInstanceMethod(targetClass, originSelector5);
//        Method swizzledMethod5 = class_getInstanceMethod(self, swizzledSelector5);
//        if (originMethod5 && swizzledMethod5) {
//            NSLog(@"[=====] Hook showAlertWithTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod5, swizzledMethod5);
//        }
//
//        SEL originSelector6 = NSSelectorFromString(@"showAlertWithAttributedTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//        SEL swizzledSelector6 = @selector(hook_showAlertWithTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:);
//        Method originMethod6 = class_getInstanceMethod(targetClass, originSelector6);
//        Method swizzledMethod6 = class_getInstanceMethod(self, swizzledSelector6);
//        if (originMethod6 && swizzledMethod6) {
//            NSLog(@"[=====] Hook showAlertWithAttributedTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons: success");
//            method_exchangeImplementations(originMethod6, swizzledMethod6);
//        }
//
//        SEL originSelector7 = NSSelectorFromString(@"showAlertView");
//        SEL swizzledSelector7 = @selector(hook_showAlertView);
//        Method originMethod7 = class_getInstanceMethod(targetClass, originSelector7);
//        Method swizzledMethod7 = class_getInstanceMethod(self, swizzledSelector7);
//        if (originMethod7 && swizzledMethod7) {
//            NSLog(@"[=====] Hook showAlertView success");
//            method_exchangeImplementations(originMethod7, swizzledMethod7);
//        }
//    });
//
//}
//
//- (void)hook_showAlertInView:(id)arg1 withTitle:(id)arg2 withSubtitle:(id)arg3 withCustomImage:(id)arg4 withDoneButtonTitle:(id)arg5 andButtons:(id)arg6 {
//    NSLog(@"[=====] Track showAlertInView:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//- (void)hook_showAlertInWindow:(id)arg1 withTitle:(id)arg2 withSubtitle:(id)arg3 withCustomImage:(id)arg4 withDoneButtonTitle:(id)arg5 andButtons:(id)arg6 {
//    NSLog(@"[=====] Track showAlertInWindow:withTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//- (void)hook_showAlertWithTitle:(id)arg1 withSubtitle:(id)arg2 withCustomImage:(id)arg3 withDoneButtonTitle:(id)arg4 andButtons:(id)arg5 {
//    NSLog(@"[=====] Track showAlertWithTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//- (void)hook_showAlertWithAttributedTitle:(id)arg1 withSubtitle:(id)arg2 withCustomImage:(id)arg3 withDoneButtonTitle:(id)arg4 andButtons:(id)arg5 {
//    NSLog(@"[=====] Track showAlertWithAttributedTitle:withSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//- (void)hook_showAlertWithTitle:(id)arg1 withAttributedSubtitle:(id)arg2 withCustomImage:(id)arg3 withDoneButtonTitle:(id)arg4 andButtons:(id)arg5 {
//    NSLog(@"[=====] Track showAlertWithTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//- (void)showAlertWithAttributedTitle:(id)arg1 withAttributedSubtitle:(id)arg2 withCustomImage:(id)arg3 withDoneButtonTitle:(id)arg4 andButtons:(id)arg5 {
//    NSLog(@"[=====] Track showAlertWithAttributedTitle:withAttributedSubtitle:withCustomImage:withDoneButtonTitle:andButtons:");
//}
//
//// showAlertView
//- (void)hook_showAlertView {
//    NSLog(@"[=====] Track showAlertView");
//}

@end
