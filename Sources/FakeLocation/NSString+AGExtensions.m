//
//  NSString+AGExtensions.m
//  AnyGoiOS
//
//  Created by <PERSON><PERSON> on 2023/7/27.
//

#import "NSString+AGExtensions.h"
#import <CommonCrypto/CommonDigest.h>

@implementation NSString (AGExtensions)

- (NSString *)mid_sub_45510 {
    const char *cStr = [self UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(cStr, (CC_LONG)strlen(cStr), result);
    NSString *resultStr = [NSString stringWithFormat:
            @"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
            result[0], result[1], result[2], result[3],
            result[4], result[5], result[6], result[7],
            result[8], result[9], result[10], result[11],
            result[12], result[13], result[14], result[15]];
    return resultStr.lowercaseString;
}

+ (NSString *)ag_decodeFromBase64String:(NSString *)string {
    NSData *decodedData = [[NSData alloc] initWithBase64EncodedString:string options:NSDataBase64DecodingIgnoreUnknownCharacters];
    return [[NSString alloc] initWithData:decodedData encoding:NSUTF8StringEncoding];
}

+ (NSString *)mid_sub_45511:(NSString *)filePath {
    NSFileHandle *fileHandle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    if (fileHandle == nil) {
        // 文件不存在或无法打开
        return nil;
    }

    CC_MD5_CTX md5Context;
    CC_MD5_Init(&md5Context);

    NSData *fileData;
    do {
        fileData = [fileHandle readDataOfLength:4096];
        CC_MD5_Update(&md5Context, [fileData bytes], (CC_LONG)[fileData length]);
    } while ([fileData length] > 0);

    [fileHandle closeFile];

    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5Context);

    NSMutableString *md5String = [NSMutableString stringWithCapacity:(CC_MD5_DIGEST_LENGTH * 2)];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", digest[i]];
    }

    return md5String.lowercaseString;
}

@end
