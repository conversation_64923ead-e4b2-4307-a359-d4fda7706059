//
//  UIAlertController+AGAdditions.m
//  data
//
//  Created by Subo on 2024/6/29.
//

#import "UIAlertController+AGAdditions.h"
#import <objc/runtime.h>
#import "AGConst.h"
#import "AppUtility.h"

static NSMutableArray<UIAlertController *> *kAlertList = nil;

@implementation UIAlertController (AGAdditions)

+ (void) load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];

        SEL originalSelector = @selector(alertControllerWithTitle:message:preferredStyle:);
        SEL swizzledSelector = @selector(ag_alertControllerWithTitle:message:preferredStyle:);

        Method originalMethod = class_getClassMethod(class, originalSelector);
        Method swizzledMethod = class_getClassMethod(class, swizzledSelector);

        method_exchangeImplementations(originalMethod, swizzledMethod);
    });
}

+ (UIAlertController *)ag_alertControllerWithTitle:(NSString *)title message:(NSString *)message preferredStyle:(UIAlertControllerStyle)preferredStyle {
    DebugLog(@"Track alertControllerWithTitle: %@ message: %@", title, message);
//    printCallStack();

    UIAlertController *alertVC = [self ag_alertControllerWithTitle:title message:message preferredStyle:preferredStyle];
    NSArray<NSString *> *valueList = @[@"SpooferPro", @"Update Required"];
    for (NSString *value in valueList) {
        if ([title containsString:value] || [message containsString:value]) {
            if (!kAlertList) {
                kAlertList = [NSMutableArray new];
            }
            [kAlertList addObject:alertVC];
            break;
        }
    }

    return alertVC;
}

//+ (void)preventForTitle:(NSString *)title message:(NSString *)message alertVC:(UIAlertController *)alertVC {
//    NSArray<NSString *> *valueList = @[@"SpooferPro", @"Update Required"];
//    for (NSString *value in valueList) {
//        if ([title containsString:value] || [message containsString:value]) {
//            if (!kAlertList) {
//                kAlertList = [NSMutableArray new];
//            }
//            [kAlertList addObject:alertVC];
//            break;
//        }
//    }
//}

// alertList
+ (NSMutableArray *)mid_sub_45521 {
    return kAlertList;
}

void printCallStack(void) {
    DebugLog(@"调用堆栈信息");
    NSArray *callStack = [NSThread callStackSymbols];
    for (NSString *symbol in callStack) {
        NSLog(@"%@", symbol);
    }
}

@end
