//
//  UIView+HookMiniView.m
//  data
//
//  Created by <PERSON><PERSON> on 14/03/2025.
//

#import "UIView+HookMiniView.h"
#import "NSObject+FMFoundation.h"
#import <objc/runtime.h>

@interface UIView (HookMiniViewPrivate)
@property (nonatomic, strong) NSTimer *hideTimer;
@end

@implementation UIView (HookMiniViewPrivate)

static char kHideTimerKey;

- (void)setHideTimer:(NSTimer *)hideTimer {
    objc_setAssociatedObject(self, &kHideTimerKey, hideTimer, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (NSTimer *)hideTimer {
    return objc_getAssociatedObject(self, &kHideTimerKey);
}

@end

@implementation UIView (HookMiniView)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [self fm_swizzleMethod:@selector(initWithFrame:) withMethod:@selector(hook_initWithFrame:)];
        [self fm_swizzleMethod:@selector(willMoveToSuperview:) withMethod:@selector(hook_willMoveToSuperview:)];
        [self fm_swizzleMethod:@selector(didMoveToSuperview) withMethod:@selector(hook_didMoveToSuperview)];
        [self hookDealloc];
    });
}

- (BOOL)isMiniView {
    return [self isKindOfClass:NSClassFromString(@"MiniView")];
}

- (void)setupHideTimer {
    if (self.hideTimer) {
        [self.hideTimer invalidate];
        self.hideTimer = nil;
    }
    
    // 使用 block API 避免循环引用
    __weak typeof(self) weakSelf = self;
    self.hideTimer = [NSTimer scheduledTimerWithTimeInterval:0.2 repeats:YES block:^(NSTimer * _Nonnull timer) {
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (strongSelf) {
            strongSelf.hidden = YES;
        }
    }];
}

- (instancetype)hook_initWithFrame:(CGRect)frame {
    UIView *obj = [self hook_initWithFrame:frame];
    if ([obj isKindOfClass:NSClassFromString(@"MiniView")] ||
        [obj isKindOfClass:NSClassFromString(@"DWBubbleMenuButton")] ||
        [obj isKindOfClass:NSClassFromString(@"JoystickController")]) {
        obj.hidden = YES;
        [obj setupHideTimer];
    }
    return obj;
}

- (void)hook_willMoveToSuperview:(nullable UIView *)newSuperview {
    [self hook_willMoveToSuperview:newSuperview];
    if ([self isMiniView]) {
        self.hidden = YES;
    }
}

- (void)hook_didMoveToSuperview {
    [self hook_didMoveToSuperview];
    if ([self isMiniView]) {
        self.hidden = YES;
    }
}

+ (void)hookDealloc {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];
        
        SEL originalSelector = NSSelectorFromString(@"dealloc");
        SEL swizzledSelector = @selector(hook_dealloc);
        
        Method originalMethod = class_getInstanceMethod(class, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(class, swizzledSelector);
        
        method_exchangeImplementations(originalMethod, swizzledMethod);
    });
}

- (void)hook_dealloc {
    if ([self isMiniView]) {
        if (self.hideTimer) {
            [self.hideTimer invalidate];
            self.hideTimer = nil;
        }
    }
    [self hook_dealloc];
}

@end
