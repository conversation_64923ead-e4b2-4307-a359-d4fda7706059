//
//  UIViewController+AGAdditions.m
//  data
//
//  Created by Sub<PERSON> on 2024/6/30.
//

#import "UIViewController+AGAdditions.h"
#import <objc/runtime.h>
#import "UIAlertController+AGAdditions.h"

@implementation UIViewController (AGAdditions)

+ (void) load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        SEL originalSelector = @selector(presentViewController:animated:completion:);
        SEL swizzledSelector = @selector(midaxxafddaax:ccaax:ddxxa:);

        Method originalMethod = class_getInstanceMethod(self, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(self, swizzledSelector);

        method_exchangeImplementations(originalMethod, swizzledMethod);
    });
}

- (void)midaxxafddaax:(UIViewController *)viewControllerToPresent ccaax: (BOOL)flag ddxxa:(void (^ __nullable)(void))completion {
    if ([viewControllerToPresent isKindOfClass:[UIAlertController class]]) {
        if ([UIAlertController.mid_sub_45521 containsObject:viewControllerToPresent]) {
            [UIAlertController.mid_sub_45521 removeObject:viewControllerToPresent];
            return;
        }
    }
    [self midaxxafddaax:viewControllerToPresent ccaax:false ddxxa:completion];
}

@end
