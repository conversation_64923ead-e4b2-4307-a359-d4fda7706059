// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		2E1FE9352DE81D32006F849D /* UnityLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE91E2DE81D32006F849D /* UnityLog.m */; };
		2E1FE9362DE81D32006F849D /* JCfdal886fdaallmd.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9292DE81D32006F849D /* JCfdal886fdaallmd.m */; };
		2E1FE9372DE81D32006F849D /* NSObject+FCAlertViewHook.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE92B2DE81D32006F849D /* NSObject+FCAlertViewHook.m */; };
		2E1FE9382DE81D32006F849D /* KCUnity.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9182DE81D32006F849D /* KCUnity.m */; };
		2E1FE9392DE81D32006F849D /* NPListener.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9242DE81D32006F849D /* NPListener.swift */; };
		2E1FE93A2DE81D32006F849D /* NSString+AGExtensions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE92D2DE81D32006F849D /* NSString+AGExtensions.m */; };
		2E1FE93B2DE81D32006F849D /* NSObject+FMFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9132DE81D32006F849D /* NSObject+FMFoundation.m */; };
		2E1FE93C2DE81D32006F849D /* UIAlertController+AGAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE92F2DE81D32006F849D /* UIAlertController+AGAdditions.m */; };
		2E1FE93D2DE81D32006F849D /* AGUnityView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9232DE81D32006F849D /* AGUnityView.m */; };
		2E1FE93E2DE81D32006F849D /* UIViewController+AGAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9332DE81D32006F849D /* UIViewController+AGAdditions.m */; };
		2E1FE93F2DE81D32006F849D /* AppUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9272DE81D32006F849D /* AppUtility.m */; };
		2E1FE9402DE81D32006F849D /* ToolInject.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE91C2DE81D32006F849D /* ToolInject.m */; };
		2E1FE9412DE81D32006F849D /* UIView+HookMiniView.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9312DE81D32006F849D /* UIView+HookMiniView.m */; };
		2E1FE9422DE81D32006F849D /* UnityTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9202DE81D32006F849D /* UnityTimer.m */; };
		2E1FE9432DE81D32006F849D /* KCUnityQuery.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE91A2DE81D32006F849D /* KCUnityQuery.m */; };
		2E1FE9442DE81D32006F849D /* NSTimer+FMFoundation.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E1FE9152DE81D32006F849D /* NSTimer+FMFoundation.m */; };
		2E1FE9452DE81D32006F849D /* UIAlertController+AGAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE92E2DE81D32006F849D /* UIAlertController+AGAdditions.h */; };
		2E1FE9462DE81D32006F849D /* KCUnityQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9192DE81D32006F849D /* KCUnityQuery.h */; };
		2E1FE9472DE81D32006F849D /* UnityTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE91F2DE81D32006F849D /* UnityTimer.h */; };
		2E1FE9482DE81D32006F849D /* NSString+AGExtensions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE92C2DE81D32006F849D /* NSString+AGExtensions.h */; };
		2E1FE9492DE81D32006F849D /* NSTimer+FMFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9142DE81D32006F849D /* NSTimer+FMFoundation.h */; };
		2E1FE94A2DE81D32006F849D /* AppUtility.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9262DE81D32006F849D /* AppUtility.h */; };
		2E1FE94B2DE81D32006F849D /* UnityLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE91D2DE81D32006F849D /* UnityLog.h */; };
		2E1FE94C2DE81D32006F849D /* KCUnity.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9172DE81D32006F849D /* KCUnity.h */; };
		2E1FE94D2DE81D32006F849D /* NSObject+FCAlertViewHook.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE92A2DE81D32006F849D /* NSObject+FCAlertViewHook.h */; };
		2E1FE94E2DE81D32006F849D /* UIView+HookMiniView.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9302DE81D32006F849D /* UIView+HookMiniView.h */; };
		2E1FE94F2DE81D32006F849D /* AGConst.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9112DE81D32006F849D /* AGConst.h */; };
		2E1FE9502DE81D32006F849D /* UIViewController+AGAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9322DE81D32006F849D /* UIViewController+AGAdditions.h */; };
		2E1FE9512DE81D32006F849D /* AGUnityView.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9222DE81D32006F849D /* AGUnityView.h */; };
		2E1FE9522DE81D32006F849D /* ToolInject.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE91B2DE81D32006F849D /* ToolInject.h */; };
		2E1FE9532DE81D32006F849D /* JCfdal886fdaallmd.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9282DE81D32006F849D /* JCfdal886fdaallmd.h */; };
		2E1FE9542DE81D32006F849D /* NSObject+FMFoundation.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E1FE9122DE81D32006F849D /* NSObject+FMFoundation.h */; };
		2E988EEC2C2E735800C35FCB /* libobjc.A.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 2E988EEB2C2E735800C35FCB /* libobjc.A.tbd */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2E1FE9112DE81D32006F849D /* AGConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AGConst.h; sourceTree = "<group>"; };
		2E1FE9122DE81D32006F849D /* NSObject+FMFoundation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+FMFoundation.h"; sourceTree = "<group>"; };
		2E1FE9132DE81D32006F849D /* NSObject+FMFoundation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+FMFoundation.m"; sourceTree = "<group>"; };
		2E1FE9142DE81D32006F849D /* NSTimer+FMFoundation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSTimer+FMFoundation.h"; sourceTree = "<group>"; };
		2E1FE9152DE81D32006F849D /* NSTimer+FMFoundation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSTimer+FMFoundation.m"; sourceTree = "<group>"; };
		2E1FE9172DE81D32006F849D /* KCUnity.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KCUnity.h; sourceTree = "<group>"; };
		2E1FE9182DE81D32006F849D /* KCUnity.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KCUnity.m; sourceTree = "<group>"; };
		2E1FE9192DE81D32006F849D /* KCUnityQuery.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KCUnityQuery.h; sourceTree = "<group>"; };
		2E1FE91A2DE81D32006F849D /* KCUnityQuery.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KCUnityQuery.m; sourceTree = "<group>"; };
		2E1FE91B2DE81D32006F849D /* ToolInject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ToolInject.h; sourceTree = "<group>"; };
		2E1FE91C2DE81D32006F849D /* ToolInject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ToolInject.m; sourceTree = "<group>"; };
		2E1FE91D2DE81D32006F849D /* UnityLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityLog.h; sourceTree = "<group>"; };
		2E1FE91E2DE81D32006F849D /* UnityLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnityLog.m; sourceTree = "<group>"; };
		2E1FE91F2DE81D32006F849D /* UnityTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnityTimer.h; sourceTree = "<group>"; };
		2E1FE9202DE81D32006F849D /* UnityTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnityTimer.m; sourceTree = "<group>"; };
		2E1FE9222DE81D32006F849D /* AGUnityView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AGUnityView.h; sourceTree = "<group>"; };
		2E1FE9232DE81D32006F849D /* AGUnityView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AGUnityView.m; sourceTree = "<group>"; };
		2E1FE9242DE81D32006F849D /* NPListener.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NPListener.swift; sourceTree = "<group>"; };
		2E1FE9262DE81D32006F849D /* AppUtility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppUtility.h; sourceTree = "<group>"; };
		2E1FE9272DE81D32006F849D /* AppUtility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppUtility.m; sourceTree = "<group>"; };
		2E1FE9282DE81D32006F849D /* JCfdal886fdaallmd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JCfdal886fdaallmd.h; sourceTree = "<group>"; };
		2E1FE9292DE81D32006F849D /* JCfdal886fdaallmd.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JCfdal886fdaallmd.m; sourceTree = "<group>"; };
		2E1FE92A2DE81D32006F849D /* NSObject+FCAlertViewHook.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+FCAlertViewHook.h"; sourceTree = "<group>"; };
		2E1FE92B2DE81D32006F849D /* NSObject+FCAlertViewHook.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+FCAlertViewHook.m"; sourceTree = "<group>"; };
		2E1FE92C2DE81D32006F849D /* NSString+AGExtensions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+AGExtensions.h"; sourceTree = "<group>"; };
		2E1FE92D2DE81D32006F849D /* NSString+AGExtensions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+AGExtensions.m"; sourceTree = "<group>"; };
		2E1FE92E2DE81D32006F849D /* UIAlertController+AGAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIAlertController+AGAdditions.h"; sourceTree = "<group>"; };
		2E1FE92F2DE81D32006F849D /* UIAlertController+AGAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIAlertController+AGAdditions.m"; sourceTree = "<group>"; };
		2E1FE9302DE81D32006F849D /* UIView+HookMiniView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+HookMiniView.h"; sourceTree = "<group>"; };
		2E1FE9312DE81D32006F849D /* UIView+HookMiniView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+HookMiniView.m"; sourceTree = "<group>"; };
		2E1FE9322DE81D32006F849D /* UIViewController+AGAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+AGAdditions.h"; sourceTree = "<group>"; };
		2E1FE9332DE81D32006F849D /* UIViewController+AGAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+AGAdditions.m"; sourceTree = "<group>"; };
		2E988ED82C2E729300C35FCB /* libdata.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libdata.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		2E988EEB2C2E735800C35FCB /* libobjc.A.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libobjc.A.tbd; path = usr/lib/libobjc.A.tbd; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2E988ED62C2E729300C35FCB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2E988EEC2C2E735800C35FCB /* libobjc.A.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2E1FE9162DE81D32006F849D /* Core */ = {
			isa = PBXGroup;
			children = (
				2E1FE9112DE81D32006F849D /* AGConst.h */,
				2E1FE9122DE81D32006F849D /* NSObject+FMFoundation.h */,
				2E1FE9132DE81D32006F849D /* NSObject+FMFoundation.m */,
				2E1FE9142DE81D32006F849D /* NSTimer+FMFoundation.h */,
				2E1FE9152DE81D32006F849D /* NSTimer+FMFoundation.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		2E1FE9212DE81D32006F849D /* Utility */ = {
			isa = PBXGroup;
			children = (
				2E1FE9172DE81D32006F849D /* KCUnity.h */,
				2E1FE9182DE81D32006F849D /* KCUnity.m */,
				2E1FE9192DE81D32006F849D /* KCUnityQuery.h */,
				2E1FE91A2DE81D32006F849D /* KCUnityQuery.m */,
				2E1FE91B2DE81D32006F849D /* ToolInject.h */,
				2E1FE91C2DE81D32006F849D /* ToolInject.m */,
				2E1FE91D2DE81D32006F849D /* UnityLog.h */,
				2E1FE91E2DE81D32006F849D /* UnityLog.m */,
				2E1FE91F2DE81D32006F849D /* UnityTimer.h */,
				2E1FE9202DE81D32006F849D /* UnityTimer.m */,
			);
			path = Utility;
			sourceTree = "<group>";
		};
		2E1FE9252DE81D32006F849D /* FakeGoPlus */ = {
			isa = PBXGroup;
			children = (
				2E1FE9212DE81D32006F849D /* Utility */,
				2E1FE9222DE81D32006F849D /* AGUnityView.h */,
				2E1FE9232DE81D32006F849D /* AGUnityView.m */,
				2E1FE9242DE81D32006F849D /* NPListener.swift */,
			);
			path = FakeGoPlus;
			sourceTree = "<group>";
		};
		2E1FE9342DE81D32006F849D /* FakeLocation */ = {
			isa = PBXGroup;
			children = (
				2E1FE9262DE81D32006F849D /* AppUtility.h */,
				2E1FE9272DE81D32006F849D /* AppUtility.m */,
				2E1FE9282DE81D32006F849D /* JCfdal886fdaallmd.h */,
				2E1FE9292DE81D32006F849D /* JCfdal886fdaallmd.m */,
				2E1FE92A2DE81D32006F849D /* NSObject+FCAlertViewHook.h */,
				2E1FE92B2DE81D32006F849D /* NSObject+FCAlertViewHook.m */,
				2E1FE92C2DE81D32006F849D /* NSString+AGExtensions.h */,
				2E1FE92D2DE81D32006F849D /* NSString+AGExtensions.m */,
				2E1FE92E2DE81D32006F849D /* UIAlertController+AGAdditions.h */,
				2E1FE92F2DE81D32006F849D /* UIAlertController+AGAdditions.m */,
				2E1FE9302DE81D32006F849D /* UIView+HookMiniView.h */,
				2E1FE9312DE81D32006F849D /* UIView+HookMiniView.m */,
				2E1FE9322DE81D32006F849D /* UIViewController+AGAdditions.h */,
				2E1FE9332DE81D32006F849D /* UIViewController+AGAdditions.m */,
			);
			path = FakeLocation;
			sourceTree = "<group>";
		};
		2E988ECF2C2E729300C35FCB = {
			isa = PBXGroup;
			children = (
				2E988EDA2C2E729300C35FCB /* Sources */,
				2E988ED92C2E729300C35FCB /* Products */,
				2E988EEA2C2E735800C35FCB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2E988ED92C2E729300C35FCB /* Products */ = {
			isa = PBXGroup;
			children = (
				2E988ED82C2E729300C35FCB /* libdata.dylib */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2E988EDA2C2E729300C35FCB /* Sources */ = {
			isa = PBXGroup;
			children = (
				2E1FE9162DE81D32006F849D /* Core */,
				2E1FE9252DE81D32006F849D /* FakeGoPlus */,
				2E1FE9342DE81D32006F849D /* FakeLocation */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		2E988EEA2C2E735800C35FCB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2E988EEB2C2E735800C35FCB /* libobjc.A.tbd */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		2E988ED42C2E729300C35FCB /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2E1FE9452DE81D32006F849D /* UIAlertController+AGAdditions.h in Headers */,
				2E1FE9462DE81D32006F849D /* KCUnityQuery.h in Headers */,
				2E1FE9472DE81D32006F849D /* UnityTimer.h in Headers */,
				2E1FE9482DE81D32006F849D /* NSString+AGExtensions.h in Headers */,
				2E1FE9492DE81D32006F849D /* NSTimer+FMFoundation.h in Headers */,
				2E1FE94A2DE81D32006F849D /* AppUtility.h in Headers */,
				2E1FE94B2DE81D32006F849D /* UnityLog.h in Headers */,
				2E1FE94C2DE81D32006F849D /* KCUnity.h in Headers */,
				2E1FE94D2DE81D32006F849D /* NSObject+FCAlertViewHook.h in Headers */,
				2E1FE94E2DE81D32006F849D /* UIView+HookMiniView.h in Headers */,
				2E1FE94F2DE81D32006F849D /* AGConst.h in Headers */,
				2E1FE9502DE81D32006F849D /* UIViewController+AGAdditions.h in Headers */,
				2E1FE9512DE81D32006F849D /* AGUnityView.h in Headers */,
				2E1FE9522DE81D32006F849D /* ToolInject.h in Headers */,
				2E1FE9532DE81D32006F849D /* JCfdal886fdaallmd.h in Headers */,
				2E1FE9542DE81D32006F849D /* NSObject+FMFoundation.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		2E988ED72C2E729300C35FCB /* data */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2E988EE32C2E729300C35FCB /* Build configuration list for PBXNativeTarget "data" */;
			buildPhases = (
				2E988ED42C2E729300C35FCB /* Headers */,
				2E988ED52C2E729300C35FCB /* Sources */,
				2E988ED62C2E729300C35FCB /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = data;
			productName = data;
			productReference = 2E988ED82C2E729300C35FCB /* libdata.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2E988ED02C2E729300C35FCB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1530;
				TargetAttributes = {
					2E988ED72C2E729300C35FCB = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = 2E988ED32C2E729300C35FCB /* Build configuration list for PBXProject "data" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2E988ECF2C2E729300C35FCB;
			productRefGroup = 2E988ED92C2E729300C35FCB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2E988ED72C2E729300C35FCB /* data */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		2E988ED52C2E729300C35FCB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2E1FE9352DE81D32006F849D /* UnityLog.m in Sources */,
				2E1FE9362DE81D32006F849D /* JCfdal886fdaallmd.m in Sources */,
				2E1FE9372DE81D32006F849D /* NSObject+FCAlertViewHook.m in Sources */,
				2E1FE9382DE81D32006F849D /* KCUnity.m in Sources */,
				2E1FE9392DE81D32006F849D /* NPListener.swift in Sources */,
				2E1FE93A2DE81D32006F849D /* NSString+AGExtensions.m in Sources */,
				2E1FE93B2DE81D32006F849D /* NSObject+FMFoundation.m in Sources */,
				2E1FE93C2DE81D32006F849D /* UIAlertController+AGAdditions.m in Sources */,
				2E1FE93D2DE81D32006F849D /* AGUnityView.m in Sources */,
				2E1FE93E2DE81D32006F849D /* UIViewController+AGAdditions.m in Sources */,
				2E1FE93F2DE81D32006F849D /* AppUtility.m in Sources */,
				2E1FE9402DE81D32006F849D /* ToolInject.m in Sources */,
				2E1FE9412DE81D32006F849D /* UIView+HookMiniView.m in Sources */,
				2E1FE9422DE81D32006F849D /* UnityTimer.m in Sources */,
				2E1FE9432DE81D32006F849D /* KCUnityQuery.m in Sources */,
				2E1FE9442DE81D32006F849D /* NSTimer+FMFoundation.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2E988EE12C2E729300C35FCB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		2E988EE22C2E729300C35FCB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
		2E988EE42C2E729300C35FCB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ZTTYNTW7NW;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = lib;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_ENABLE_CPP_RTTI = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				INSTALL_PATH = "@executable_path/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				OTHER_CFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		2E988EE52C2E729300C35FCB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = ZTTYNTW7NW;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = lib;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_ENABLE_CPP_RTTI = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				INSTALL_PATH = "@executable_path/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				OTHER_CFLAGS = "";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2E988ED32C2E729300C35FCB /* Build configuration list for PBXProject "data" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E988EE12C2E729300C35FCB /* Debug */,
				2E988EE22C2E729300C35FCB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2E988EE32C2E729300C35FCB /* Build configuration list for PBXNativeTarget "data" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2E988EE42C2E729300C35FCB /* Debug */,
				2E988EE52C2E729300C35FCB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2E988ED02C2E729300C35FCB /* Project object */;
}
